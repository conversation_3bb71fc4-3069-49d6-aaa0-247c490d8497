package com.hdhe.scantest;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class LoginActivity extends AppCompatActivity {
    
    private TextView tvApiUrl;
    private EditText etUsername;
    private EditText etPassword;
    private Button btnLogin;
    private Button btnResetApi;
    private String apiUrl;
    private ConfigManager configManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        
        initViews();
        configManager = new ConfigManager(this);
        
        // 获取传递过来的API地址
        apiUrl = getIntent().getStringExtra("api_url");
        if (TextUtils.isEmpty(apiUrl)) {
            apiUrl = configManager.getApiUrl();
        }
        
        tvApiUrl.setText("API地址: " + apiUrl);
    }
    
    private void initViews() {
        tvApiUrl = findViewById(R.id.tv_api_url);
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        btnLogin = findViewById(R.id.btn_login);
        btnResetApi = findViewById(R.id.btn_reset_api);
        
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performLogin();
            }
        });
        
        btnResetApi.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                resetApiConfiguration();
            }
        });
    }
    
    private void performLogin() {
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        
        if (TextUtils.isEmpty(username)) {
            Toast.makeText(this, "请输入用户名", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (TextUtils.isEmpty(password)) {
            Toast.makeText(this, "请输入密码", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 这里可以添加实际的登录逻辑
        // 例如：调用API进行用户验证
        
        // 模拟登录过程
        Toast.makeText(this, "正在登录...", Toast.LENGTH_SHORT).show();
        
        // 模拟登录成功，跳转到主页面
        // 实际项目中这里应该是异步网络请求
        simulateLogin(username, password);
    }
    
    private void simulateLogin(String username, String password) {
        // 简单的模拟登录验证（实际项目中应该调用API）
        if ("admin".equals(username) && "123456".equals(password)) {
            Toast.makeText(this, "登录成功", Toast.LENGTH_SHORT).show();
            
            // 保存登录状态
            configManager.saveLoginStatus(true);
            configManager.saveUsername(username);
            
            // 跳转到主页面
            Intent intent = new Intent(LoginActivity.this, MainActivity.class);
            intent.putExtra("api_url", apiUrl);
            intent.putExtra("username", username);
            startActivity(intent);
            finish();
        } else {
            Toast.makeText(this, "用户名或密码错误", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void resetApiConfiguration() {
        // 清除API配置，返回检测页面
        configManager.clearApiUrl();
        Toast.makeText(this, "API配置已清除", Toast.LENGTH_SHORT).show();
        
        Intent intent = new Intent(LoginActivity.this, SplashActivity.class);
        startActivity(intent);
        finish();
    }
}
