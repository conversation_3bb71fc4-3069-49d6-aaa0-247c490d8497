package com.hdhe.scantest;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class SplashActivity extends AppCompatActivity {
    
    private TextView tvTitle;
    private TextView tvStatus;
    private LinearLayout layoutApiInput;
    private EditText etApiUrl;
    private Button btnConfirm;
    private ConfigManager configManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);
        
        initViews();
        configManager = new ConfigManager(this);
        
        // 延迟检测，显示启动画面效果
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                checkApiConfiguration();
            }
        }, 2000);
    }
    
    private void initViews() {
        tvTitle = findViewById(R.id.tv_title);
        tvStatus = findViewById(R.id.tv_status);
        layoutApiInput = findViewById(R.id.layout_api_input);
        etApiUrl = findViewById(R.id.et_api_url);
        btnConfirm = findViewById(R.id.btn_confirm);
        
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveApiUrlAndProceed();
            }
        });
    }
    
    private void checkApiConfiguration() {
        tvStatus.setText("正在检测配置...");
        
        String apiUrl = configManager.getApiUrl();
        
        if (!TextUtils.isEmpty(apiUrl)) {
            // 有API地址，直接跳转到登录页面
            tvStatus.setText("配置检测完成，跳转到登录页面...");
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    goToLoginActivity(apiUrl);
                }
            }, 1000);
        } else {
            // 没有API地址，显示输入界面
            tvStatus.setText("未检测到API配置，请输入API地址");
            layoutApiInput.setVisibility(View.VISIBLE);
        }
    }
    
    private void saveApiUrlAndProceed() {
        String apiUrl = etApiUrl.getText().toString().trim();
        
        if (TextUtils.isEmpty(apiUrl)) {
            Toast.makeText(this, "请输入API地址", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 简单的URL格式验证
        if (!apiUrl.startsWith("http://") && !apiUrl.startsWith("https://")) {
            Toast.makeText(this, "请输入有效的API地址（以http://或https://开头）", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 保存API地址
        configManager.saveApiUrl(apiUrl);
        
        Toast.makeText(this, "API地址保存成功", Toast.LENGTH_SHORT).show();
        
        // 跳转到登录页面
        goToLoginActivity(apiUrl);
    }
    
    private void goToLoginActivity(String apiUrl) {
        Intent intent = new Intent(SplashActivity.this, LoginActivity.class);
        intent.putExtra("api_url", apiUrl);
        startActivity(intent);
        finish();
    }
}
