package com.hdhe.scantest;

/**
 * API响应结果类
 */
public class ApiResult {
    
    /**
     * 响应结果标识
     * -1:session超时
     * 0：失败
     * 1：成功
     * 2+：由各业务存储过程自定义
     */
    public int flag = 0;
    
    /**
     * 响应结果消息
     */
    public String msg = "执行失败。";
    
    /**
     * 返回数据
     */
    public Data data = new Data();
    
    /**
     * 数据类
     */
    public static class Data {
        // 可以根据需要添加具体的数据字段
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return flag == 1;
    }
    
    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return flag == 0;
    }
    
    /**
     * 判断是否session超时
     */
    public boolean isSessionTimeout() {
        return flag == -1;
    }
}
