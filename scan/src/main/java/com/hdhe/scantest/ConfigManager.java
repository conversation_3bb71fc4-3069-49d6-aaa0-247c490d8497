package com.hdhe.scantest;

import android.content.Context;
import android.content.SharedPreferences;

public class ConfigManager {
    
    private static final String PREF_NAME = "app_config";
    private static final String KEY_API_URL = "api_url";
    private static final String KEY_LOGIN_STATUS = "login_status";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_DEVICE_CODE = "device_code";
    private static final String KEY_SESSION_ID = "session_id";
    
    private SharedPreferences sharedPreferences;
    private SharedPreferences.Editor editor;
    
    public ConfigManager(Context context) {
        sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = sharedPreferences.edit();
    }
    
    /**
     * 保存API地址
     */
    public void saveApiUrl(String apiUrl) {
        editor.putString(KEY_API_URL, apiUrl);
        editor.apply();
    }
    
    /**
     * 获取API地址
     */
    public String getApiUrl() {
        return sharedPreferences.getString(KEY_API_URL, "");
    }
    
    /**
     * 清除API地址
     */
    public void clearApiUrl() {
        editor.remove(KEY_API_URL);
        editor.apply();
    }
    
    /**
     * 保存登录状态
     */
    public void saveLoginStatus(boolean isLoggedIn) {
        editor.putBoolean(KEY_LOGIN_STATUS, isLoggedIn);
        editor.apply();
    }
    
    /**
     * 获取登录状态
     */
    public boolean isLoggedIn() {
        return sharedPreferences.getBoolean(KEY_LOGIN_STATUS, false);
    }
    
    /**
     * 保存用户名
     */
    public void saveUsername(String username) {
        editor.putString(KEY_USERNAME, username);
        editor.apply();
    }
    
    /**
     * 获取用户名
     */
    public String getUsername() {
        return sharedPreferences.getString(KEY_USERNAME, "");
    }
    
    /**
     * 清除所有配置
     */
    public void clearAll() {
        editor.clear();
        editor.apply();
    }
    
    /**
     * 退出登录
     */
    public void logout() {
        editor.remove(KEY_LOGIN_STATUS);
        editor.remove(KEY_USERNAME);
        editor.apply();
    }
}
