package com.hdhe.scantest;

import androidx.appcompat.app.AppCompatActivity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.TextView;
import android.widget.Toast;

public class MainActivity extends AppCompatActivity {

    private String apiUrl;
    private String username;
    private ConfigManager configManager;
    private TextView tvWelcome;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        configManager = new ConfigManager(this);

        // 获取传递过来的参数
        apiUrl = getIntent().getStringExtra("api_url");
        username = getIntent().getStringExtra("username");

        // 如果没有传递参数，从配置中获取
        if (apiUrl == null) {
            apiUrl = configManager.getApiUrl();
        }
        if (username == null) {
            username = configManager.getUsername();
        }

        initViews();
        setupWelcomeMessage();
    }

    private void initViews() {
        tvWelcome = findViewById(R.id.tv_welcome);
    }

    private void setupWelcomeMessage() {
        if (tvWelcome != null && username != null) {
            tvWelcome.setText("欢迎, " + username);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.action_logout) {
            logout();
            return true;
        } else if (id == R.id.action_settings) {
            showSettings();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    private void logout() {
        configManager.logout();
        Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show();

        Intent intent = new Intent(MainActivity.this, SplashActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    private void showSettings() {
        Toast.makeText(this, "API地址: " + apiUrl, Toast.LENGTH_LONG).show();
    }
}
