package com.hdhe.scantest;

import android.os.AsyncTask;
import android.util.Log;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 网络请求工具类
 */
public class NetworkUtils {
    
    private static final String TAG = "NetworkUtils";
    private static final int TIMEOUT = 10000; // 10秒超时
    
    /**
     * 网络请求回调接口
     */
    public interface NetworkCallback {
        void onSuccess(ApiResult result);
        void onError(String error);
    }
    
    /**
     * 检查设备码是否已绑定账号
     */
    public static void checkDeviceCode(String apiUrl, String code, NetworkCallback callback) {
        new CheckCodeTask(callback).execute(apiUrl, code);
    }
    
    /**
     * 异步任务执行网络请求
     */
    private static class CheckCodeTask extends AsyncTask<String, Void, ApiResult> {
        
        private NetworkCallback callback;
        private String errorMessage;
        
        public CheckCodeTask(NetworkCallback callback) {
            this.callback = callback;
        }
        
        @Override
        protected ApiResult doInBackground(String... params) {
            String apiUrl = params[0];
            String code = params[1];
            
            try {
                // 构建完整的API URL
                String fullUrl = apiUrl;
                if (!apiUrl.endsWith("/")) {
                    fullUrl += "/";
                }
                fullUrl += "api/app/account/loginbycode?code=" + code;
                
                Log.d(TAG, "请求URL: " + fullUrl);
                
                // 创建HTTP连接
                URL url = new URL(fullUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(TIMEOUT);
                connection.setReadTimeout(TIMEOUT);
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Accept", "application/json");
                
                // 获取响应码
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "响应码: " + responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 读取响应内容
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), "UTF-8"));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String responseBody = response.toString();
                    Log.d(TAG, "响应内容: " + responseBody);
                    
                    // 解析JSON响应
                    return parseResponse(responseBody);
                } else {
                    errorMessage = "HTTP错误: " + responseCode;
                    return null;
                }
                
            } catch (Exception e) {
                Log.e(TAG, "网络请求异常", e);
                errorMessage = "网络请求失败: " + e.getMessage();
                return null;
            }
        }
        
        @Override
        protected void onPostExecute(ApiResult result) {
            if (result != null) {
                callback.onSuccess(result);
            } else {
                callback.onError(errorMessage != null ? errorMessage : "未知错误");
            }
        }
        
        /**
         * 解析API响应
         */
        private ApiResult parseResponse(String responseBody) {
            try {
                JSONObject json = new JSONObject(responseBody);
                ApiResult result = new ApiResult();
                
                if (json.has("flag")) {
                    result.flag = json.getInt("flag");
                }
                if (json.has("msg")) {
                    result.msg = json.getString("msg");
                }
                
                Log.d(TAG, "解析结果 - flag: " + result.flag + ", msg: " + result.msg);
                return result;
                
            } catch (Exception e) {
                Log.e(TAG, "JSON解析异常", e);
                ApiResult result = new ApiResult();
                result.flag = 0;
                result.msg = "响应解析失败";
                return result;
            }
        }
    }
}
