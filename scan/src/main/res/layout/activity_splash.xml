<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="20dp"
    android:background="#f5f5f5">

    <!-- 应用图标 -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="30dp"
        android:src="@mipmap/ic_launcher"
        android:contentDescription="应用图标" />

    <!-- 应用标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="春蕾扫码系统"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="20dp" />

    <!-- 状态提示 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="正在初始化..."
        android:textSize="16sp"
        android:textColor="#666666"
        android:layout_marginBottom="40dp" />

    <!-- API地址输入区域 -->
    <LinearLayout
        android:id="@+id/layout_api_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        android:background="#ffffff"
        android:padding="20dp"
        android:elevation="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请输入API服务器地址："
            android:textSize="16sp"
            android:textColor="#333333"
            android:layout_marginBottom="10dp" />

        <EditText
            android:id="@+id/et_api_url"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="例如：https://api.example.com"
            android:inputType="textUri"
            android:background="@drawable/edit_text_background"
            android:padding="12dp"
            android:textSize="14sp"
            android:layout_marginBottom="20dp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="确认"
            android:textSize="16sp"
            android:textColor="#ffffff"
            android:background="@drawable/button_background"
            android:elevation="2dp" />

    </LinearLayout>

    <!-- 版本信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="版本 1.5"
        android:textSize="12sp"
        android:textColor="#999999"
        android:layout_marginTop="40dp" />

</LinearLayout>
