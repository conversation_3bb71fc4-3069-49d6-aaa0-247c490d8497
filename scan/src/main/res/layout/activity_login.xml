<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="20dp"
    android:background="#f5f5f5">

    <!-- 登录标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="用户登录"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="10dp" />

    <!-- API地址显示 -->
    <TextView
        android:id="@+id/tv_api_url"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="API地址: "
        android:textSize="12sp"
        android:textColor="#666666"
        android:layout_marginBottom="30dp" />

    <!-- 登录表单 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="20dp"
        android:elevation="4dp">

        <!-- 用户名输入 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="用户名："
            android:textSize="16sp"
            android:textColor="#333333"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/et_username"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="请输入用户名"
            android:inputType="text"
            android:background="@drawable/edit_text_background"
            android:padding="12dp"
            android:textSize="14sp"
            android:layout_marginBottom="16dp" />

        <!-- 密码输入 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="密码："
            android:textSize="16sp"
            android:textColor="#333333"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="请输入密码"
            android:inputType="textPassword"
            android:background="@drawable/edit_text_background"
            android:padding="12dp"
            android:textSize="14sp"
            android:layout_marginBottom="24dp" />

        <!-- 登录按钮 -->
        <Button
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="登录"
            android:textSize="16sp"
            android:textColor="#ffffff"
            android:background="@drawable/button_background"
            android:elevation="2dp"
            android:layout_marginBottom="12dp" />

        <!-- 重置API按钮 -->
        <Button
            android:id="@+id/btn_reset_api"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:text="重新配置API地址"
            android:textSize="14sp"
            android:textColor="#666666"
            android:background="@drawable/button_secondary_background"
            android:elevation="1dp" />

    </LinearLayout>

    <!-- 提示信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="默认用户名: admin, 密码: 123456"
        android:textSize="12sp"
        android:textColor="#999999"
        android:layout_marginTop="20dp" />

</LinearLayout>
